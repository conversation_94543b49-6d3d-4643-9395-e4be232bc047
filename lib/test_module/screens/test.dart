import 'package:app/i18n/i18n.dart';
import 'package:app/shared/cubits/api_connectivity/cubit.dart';
import 'package:app/shared/repositories/test_task.dart';
import 'package:app/shared/screens/app_menu.dart';
import 'package:app/shared/widgets/app_bar/dynamic_app_bar_v3.dart';
import 'package:app/shared/widgets/wrapper/api_connectivity_listener.dart';
import 'package:app/shared/widgets/wrapper/v3_theme_wrapper.dart';
import 'package:app/test_module/cubits/cubit.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:material_symbols_icons/symbols.dart';

class TestScreen extends StatelessWidget {
  const TestScreen({Key? key}) : super(key: key);

  static MaterialPageRoute<void> route() {
    return MaterialPageRoute<void>(
      settings: const RouteSettings(name: '/test'),
      builder: (_) => const TestScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentConnectivityState = context.read<ApiConnectivityCubit>().state;
    print('TestScreen build - current connectivity state: $currentConnectivityState');

    return BlocProvider(
      create: (context) {
        final cubit = TestCubit(
          context.read<TestTaskRepository>(),
          defaultFilterValues: {},
          isOnline: currentConnectivityState,
          language: context.read<LocalizationState>().locale.toString(),
        );
        print('About to call initial load()');
        cubit.load();
        return cubit;
      },
      child: ApiConnectivityListener(
        onConnectionStateChanged: (context, isOnline) {
          print('Network state changed: $isOnline');
          context.read<TestCubit>().updateNetworkState(isOnline);
          if (isOnline) {
            print('Network is online, calling load()');
            context.read<TestCubit>().load();
          }
        },
        child: const TestTaskView(),
      ),
    );
  }
}

class TestTaskView extends StatelessWidget {
  const TestTaskView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tr = context.translator;

    return V3ThemeWrapper(
      child: Scaffold(
        drawer: const AppMenu(),
        appBar: DynamicAppBarV3(
          title: tr(Labels.navigation.test),
        ),
        body: Scaffold(
          body: Center(
            child: FLNoData.note(title: tr(Labels.navigation.test)),
          ),
          floatingActionButton: FLFloatingActionButton(
            symbol: const FLSymbol(Symbols.add),
            onPressed: () {},
          ),
          bottomNavigationBar: FLBottomBar.flat(
            child: FLFilledButton.text(
              text: tr(Labels.action.test_button_label),
              onPressed: () {},
            ),
          ),
        ),
      ),
    );
  }
}
