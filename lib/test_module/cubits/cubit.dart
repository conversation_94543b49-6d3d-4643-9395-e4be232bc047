import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/cubits/paged_entity_list/cubit.dart';
import 'package:app/shared/repositories/test_task.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:app/test_module/models/test_task.dart';

class TestCubit extends PagedEntityListCubit<TestModel, TestStateExtension> {
  TestCubit(
    this._repository, {
    required Map<String, FilterValueModel> defaultFilterValues,
    required bool isOnline,
    required String language,
    int pageSize = 15,
  }) : _language = language,
       super(
          defaultFilterValues: defaultFilterValues,
          initialStateExtension: TestStateExtension(
            language: language,
            status: null,
          ),
          isOnline: isOnline,
          pageSize: pageSize,
          networkDependent: true,
        );

  final TestTaskRepository _repository;
  final String _language;

  @override
  Future<Result<List<TestModel>, AppError>> loadEntities(
    int pageSize,
    int pageIndex,
    Map<String, FilterValueModel>? filterValues,
  ) {
    print('loadEntities called with pageSize: $pageSize, pageIndex: $pageIndex');
    return _repository.loadEntities(
      filterValues: filterValues!,
      page: pageIndex,
      pageSize: pageSize,
      language: _language,
    );
  }

  @override
  Future<void> load({bool background = false}) async {
    print('TestCubit.load() called, isOnline: ${state.isOnline}, networkDependent: $isNetworkDependent');
    return super.load(background: background);
  }
}

class TestStateExtension {
  final String language;
  final String? status;

  const TestStateExtension({
    required this.language,
    required this.status,
  });
}
